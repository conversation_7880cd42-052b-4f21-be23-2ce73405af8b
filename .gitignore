# Python bytecode
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing and coverage
.pytest_cache/
.coverage
htmlcov/
.pybuilder/
.tox/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.project
.pydevproject
.settings/
*.sublime-project
*.sublime-workspace

# DeepSeek CLI specific
*.log
.deepseek_history
.deepseek_cache/
.api_key
issues_and_fixes.md
IMPROVEMENTS.md
TASKS.md

# OS specific
.DS_Store
Thumbs.db
*.bak
*.tmp
*.temp
desktop.ini

# CI/CD
.github/workflows/*.yml.bak

# Documentation
/site
docs/_build/