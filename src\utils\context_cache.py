"""Context caching system for DeepSeek CLI"""

import hashlib
import json
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import os
import tempfile

# Add proper import paths for both development and installed modes
try:
    # When running as an installed package
    from config.settings import CACHE_MIN_TOKENS, CACHE_MAX_SIZE, CACHE_TTL_HOURS
except ImportError:
    # When running in development mode
    from src.config.settings import CACHE_MIN_TOKENS, CACHE_MAX_SIZE, CACHE_TTL_HOURS


@dataclass
class CacheEntry:
    """Represents a cached context entry"""
    context_hash: str
    messages: List[Dict[str, Any]]
    token_count: int
    created_at: float
    last_accessed: float
    hit_count: int = 0


class ContextCache:
    """Manages context caching for conversation history"""
    
    def __init__(self, cache_dir: Optional[str] = None):
        """Initialize the context cache
        
        Args:
            cache_dir: Directory to store cache files. If None, uses system temp directory.
        """
        self.cache_dir = cache_dir or os.path.join(tempfile.gettempdir(), "deepseek_cli_cache")
        self.cache_file = os.path.join(self.cache_dir, "context_cache.json")
        self.cache: Dict[str, CacheEntry] = {}
        self.enabled = True
        self.stats = {
            "hits": 0,
            "misses": 0,
            "total_tokens_saved": 0
        }
        
        # Ensure cache directory exists
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # Load existing cache
        self._load_cache()
        
        # Clean expired entries
        self._cleanup_expired()
    
    def _generate_context_hash(self, messages: List[Dict[str, Any]]) -> str:
        """Generate a hash for the message context"""
        # Create a normalized representation of messages for hashing
        normalized = []
        for msg in messages:
            # Only include role and content for hashing
            normalized.append({
                "role": msg.get("role", ""),
                "content": msg.get("content", "")
            })
        
        context_str = json.dumps(normalized, sort_keys=True)
        return hashlib.sha256(context_str.encode()).hexdigest()
    
    def _estimate_token_count(self, messages: List[Dict[str, Any]]) -> int:
        """Estimate token count for messages (rough approximation)"""
        total_chars = 0
        for msg in messages:
            content = msg.get("content", "")
            if isinstance(content, str):
                total_chars += len(content)
        
        # Rough approximation: 4 characters per token on average
        return max(1, total_chars // 4)
    
    def _load_cache(self) -> None:
        """Load cache from disk"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Convert dict entries back to CacheEntry objects
                for key, entry_data in data.get("entries", {}).items():
                    self.cache[key] = CacheEntry(**entry_data)
                
                # Load stats
                self.stats.update(data.get("stats", {}))
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"Warning: Failed to load cache file: {e}")
            self.cache = {}
    
    def _save_cache(self) -> None:
        """Save cache to disk"""
        try:
            cache_data = {
                "entries": {k: asdict(v) for k, v in self.cache.items()},
                "stats": self.stats
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            print(f"Warning: Failed to save cache file: {e}")
    
    def _cleanup_expired(self) -> None:
        """Remove expired cache entries"""
        current_time = time.time()
        ttl_seconds = CACHE_TTL_HOURS * 3600
        
        expired_keys = []
        for key, entry in self.cache.items():
            if current_time - entry.created_at > ttl_seconds:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        if expired_keys:
            self._save_cache()
    
    def _evict_lru(self) -> None:
        """Evict least recently used entries if cache is too large"""
        if len(self.cache) <= CACHE_MAX_SIZE:
            return
        
        # Sort by last accessed time and remove oldest entries
        sorted_entries = sorted(
            self.cache.items(),
            key=lambda x: x[1].last_accessed
        )
        
        entries_to_remove = len(self.cache) - CACHE_MAX_SIZE + 1
        for i in range(entries_to_remove):
            key = sorted_entries[i][0]
            del self.cache[key]
        
        self._save_cache()
    
    def get_cached_context(self, messages: List[Dict[str, Any]]) -> Optional[Tuple[str, int]]:
        """Get cached context if available
        
        Args:
            messages: List of conversation messages
            
        Returns:
            Tuple of (context_hash, token_count) if cache hit, None if cache miss
        """
        if not self.enabled or not messages:
            return None
        
        # Only cache if we have enough tokens
        estimated_tokens = self._estimate_token_count(messages)
        if estimated_tokens < CACHE_MIN_TOKENS:
            return None
        
        context_hash = self._generate_context_hash(messages)
        
        if context_hash in self.cache:
            entry = self.cache[context_hash]
            
            # Update access statistics
            entry.last_accessed = time.time()
            entry.hit_count += 1
            
            # Update global stats
            self.stats["hits"] += 1
            self.stats["total_tokens_saved"] += entry.token_count
            
            self._save_cache()
            return context_hash, entry.token_count
        
        # Cache miss
        self.stats["misses"] += 1
        return None
    
    def cache_context(self, messages: List[Dict[str, Any]], actual_token_count: Optional[int] = None) -> str:
        """Cache the current context
        
        Args:
            messages: List of conversation messages
            actual_token_count: Actual token count from API response (if available)
            
        Returns:
            Context hash for the cached entry
        """
        if not self.enabled or not messages:
            return ""
        
        estimated_tokens = self._estimate_token_count(messages)
        if estimated_tokens < CACHE_MIN_TOKENS:
            return ""
        
        context_hash = self._generate_context_hash(messages)
        current_time = time.time()
        
        # Use actual token count if provided, otherwise use estimate
        token_count = actual_token_count or estimated_tokens
        
        # Create cache entry
        entry = CacheEntry(
            context_hash=context_hash,
            messages=messages.copy(),
            token_count=token_count,
            created_at=current_time,
            last_accessed=current_time
        )
        
        self.cache[context_hash] = entry
        
        # Perform cleanup
        self._evict_lru()
        self._save_cache()
        
        return context_hash
    
    def toggle_cache(self) -> bool:
        """Toggle cache enabled/disabled state
        
        Returns:
            New enabled state
        """
        self.enabled = not self.enabled
        return self.enabled
    
    def clear_cache(self) -> None:
        """Clear all cached entries"""
        self.cache.clear()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "total_tokens_saved": 0
        }
        self._save_cache()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics
        
        Returns:
            Dictionary containing cache statistics
        """
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = (self.stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            "enabled": self.enabled,
            "total_entries": len(self.cache),
            "cache_hits": self.stats["hits"],
            "cache_misses": self.stats["misses"],
            "hit_rate_percent": round(hit_rate, 2),
            "total_tokens_saved": self.stats["total_tokens_saved"],
            "cache_size_mb": self._get_cache_size_mb()
        }
    
    def _get_cache_size_mb(self) -> float:
        """Get cache file size in MB"""
        try:
            if os.path.exists(self.cache_file):
                size_bytes = os.path.getsize(self.cache_file)
                return round(size_bytes / (1024 * 1024), 2)
        except OSError:
            pass
        return 0.0
